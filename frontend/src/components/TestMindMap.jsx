import React, { useEffect, useRef } from 'react';
import { Markmap } from 'markmap-view';
import { Transformer } from 'markmap-lib';
import * as d3 from 'd3';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';

const TestMindMap = () => {
  const svgRef = useRef();
  const markmapRef = useRef();

  useEffect(() => {
    console.log('=== TestMindMap useEffect START ===');

    // 简单的测试数据
    const markdown = `
# 测试用例总览

## 高优先级测试
- 登录功能测试
- 数据保存测试

## 中优先级测试
- 界面显示测试
- 性能测试

## 低优先级测试
- 兼容性测试
- 边界测试
`;

    console.log('Test markdown:', markdown);
    console.log('SVG ref:', svgRef.current);

    // 延迟执行，确保DOM已渲染
    const timeoutId = setTimeout(() => {
      console.log('Delayed execution - SVG ref:', svgRef.current);

      if (svgRef.current) {
        console.log('✅ SVG ref available, creating test mindmap');

        // 首先测试d3是否正常工作
        console.log('Testing d3...');
        const svg = d3.select(svgRef.current);
        console.log('d3 svg selection:', svg);

        // 添加一个测试圆圈
        svg.selectAll('*').remove(); // 清空
        svg.append('circle')
          .attr('cx', 100)
          .attr('cy', 100)
          .attr('r', 20)
          .attr('fill', 'red');

        console.log('d3 test circle added');

        try {
          console.log('Creating transformer...');
          const transformer = new Transformer();
          console.log('Transformer created:', transformer);

          console.log('Transforming markdown...');
          const { root } = transformer.transform(markdown);
          console.log('✅ Transformed root:', root);

          if (markmapRef.current) {
            console.log('Destroying previous markmap...');
            markmapRef.current.destroy();
          }

          console.log('Creating markmap...');
          console.log('SVG element:', svgRef.current);
          console.log('SVG dimensions:', {
            width: svgRef.current.clientWidth,
            height: svgRef.current.clientHeight,
            offsetWidth: svgRef.current.offsetWidth,
            offsetHeight: svgRef.current.offsetHeight
          });

          // 清空SVG内容
          svgRef.current.innerHTML = '';

          // 设置SVG属性
          svgRef.current.setAttribute('width', '100%');
          svgRef.current.setAttribute('height', '100%');
          svgRef.current.setAttribute('viewBox', '0 0 800 600');

          console.log('Creating Markmap instance...');
          markmapRef.current = Markmap.create(svgRef.current, {
            colorFreezeLevel: 2,
            duration: 300,
            maxWidth: 300,
            spacingVertical: 8,
            spacingHorizontal: 80,
            autoFit: true,
            pan: true,
            zoom: true
          }, root);

          console.log('Markmap instance created:', markmapRef.current);

          // 手动触发渲染
          if (markmapRef.current && markmapRef.current.fit) {
            console.log('Calling fit method...');
            markmapRef.current.fit();
          }

          console.log('✅ Test MindMap created successfully!', markmapRef.current);
        } catch (error) {
          console.error('❌ Error creating test mindmap:', error);
          console.error('Error stack:', error.stack);
        }
      } else {
        console.error('❌ SVG ref still not available after delay');
      }
    }, 1000);

    return () => {
      clearTimeout(timeoutId);
      if (markmapRef.current) {
        markmapRef.current.destroy();
      }
    };
  }, []);

  return (
    <Paper elevation={3} sx={{ p: 3, height: '600px' }}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        测试思维导图
      </Typography>
      <Box sx={{ width: '100%', height: '500px', border: '1px solid #ccc', backgroundColor: '#fff' }}>
        <svg
          ref={svgRef}
          style={{
            width: '100%',
            height: '100%',
            display: 'block',
            fontFamily: 'Arial, sans-serif'
          }}
        />
      </Box>

      {/* 添加基本的markmap样式 */}
      <style jsx>{`
        svg .markmap-node > circle {
          cursor: pointer;
        }
        svg .markmap-node-text {
          fill: #000;
          font-size: 12px;
          cursor: pointer;
        }
        svg .markmap-link {
          fill: none;
          stroke: #ccc;
          stroke-width: 2px;
        }
      `}</style>
    </Paper>
  );
};

export default TestMindMap;
