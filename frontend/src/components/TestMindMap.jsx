import React, { useEffect, useRef } from 'react';
import { Markmap } from 'markmap-view';
import { Transformer } from 'markmap-lib';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';

const TestMindMap = () => {
  const svgRef = useRef();
  const markmapRef = useRef();

  useEffect(() => {
    console.log('TestMindMap useEffect triggered');
    
    // 简单的测试数据
    const markdown = `
# 测试用例总览

## 高优先级测试
- 登录功能测试
- 数据保存测试

## 中优先级测试
- 界面显示测试
- 性能测试

## 低优先级测试
- 兼容性测试
- 边界测试
`;

    console.log('Test markdown:', markdown);

    if (svgRef.current) {
      console.log('SVG ref available');
      
      try {
        const transformer = new Transformer();
        const { root } = transformer.transform(markdown);
        console.log('Transformed root:', root);

        if (markmapRef.current) {
          markmapRef.current.destroy();
        }

        markmapRef.current = Markmap.create(svgRef.current, {
          colorFreezeLevel: 2,
          duration: 300,
          maxWidth: 300,
          spacingVertical: 8,
          spacingHorizontal: 80,
          autoFit: true,
          pan: true,
          zoom: true
        }, root);

        console.log('Test MindMap created successfully!');
      } catch (error) {
        console.error('Error creating test mindmap:', error);
      }
    } else {
      console.error('SVG ref not available');
    }

    return () => {
      if (markmapRef.current) {
        markmapRef.current.destroy();
      }
    };
  }, []);

  return (
    <Paper elevation={3} sx={{ p: 3, height: '600px' }}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        测试思维导图
      </Typography>
      <Box sx={{ width: '100%', height: '500px', border: '1px solid #ccc' }}>
        <svg
          ref={svgRef}
          style={{
            width: '100%',
            height: '100%'
          }}
        />
      </Box>
    </Paper>
  );
};

export default TestMindMap;
